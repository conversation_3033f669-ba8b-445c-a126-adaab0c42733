# Social Media Post Generator

A modern web application built with Nuxt 3 that generates engaging social media posts using Google Gemini AI.

## Features

- 🤖 AI-powered content generation using Google Gemini
- 🎭 Multiple tone options (Professional, Casual, Witty, Inspirational)
- 📱 Responsive design with beautiful UI
- 🏷️ Automatic hashtag generation
- 📋 One-click copy to clipboard
- ⚡ Fast and modern Nuxt 3 framework
- 🔒 Secure API with origin validation, rate limiting, and input validation

## Setup

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd post-generator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your Google Gemini API key:
   ```
   GEMINI_API_KEY=your-actual-gemini-api-key-here
   ```

4. **Get a Gemini API Key**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the key to your `.env` file

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## Usage

1. Enter a topic or idea for your social media post
2. Choose your preferred tone (Professional, Casual, Witty, or Inspirational)
3. Click "Generate Post" to create your content
4. Copy the generated post and hashtags to your clipboard
5. Use the content on your favorite social media platform!

## Technology Stack

- **Framework**: Nuxt 3
- **AI Provider**: Google Gemini AI
- **Styling**: Tailwind CSS
- **Language**: TypeScript
- **Runtime**: Node.js

## API Endpoints

- `POST /api/generate` - Generates social media posts
  - Body: `{ prompt: string, tone: string }`
  - Response: `{ text: string, hashtags: string }`
  - Security: Origin validation, rate limiting, input validation

## Security

This application includes comprehensive security measures to prevent API misuse:

- **Origin Validation**: Only accepts requests from authorized domains
- **Rate Limiting**: 10 requests per minute per IP address
- **Input Validation**: Validates prompt length and tone parameters
- **Method Validation**: Only accepts POST requests
- **User-Agent Filtering**: Blocks automated tools in development

For detailed security information, see [SECURITY.md](SECURITY.md).

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is open source and available under the [MIT License](LICENSE).
