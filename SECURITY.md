# API Security Measures

This document outlines the security measures implemented to protect the `/api/generate` endpoint from misuse.

## 🔒 Security Features Implemented

### 1. Origin Validation
- **Purpose**: Prevents Cross-Site Request Forgery (CSRF) and unauthorized API usage
- **Implementation**: Validates `Origin` and `Referer` headers against allowed domains
- **Allowed Origins** (Development):
  - `http://localhost:3000`
  - `https://localhost:3000`
  - `http://127.0.0.1:3000`
  - `https://127.0.0.1:3000`

### 2. Rate Limiting
- **Purpose**: Prevents abuse and DoS attacks
- **Limits**: 10 requests per minute per IP address
- **Implementation**: In-memory rate limiting (for production, consider Redis)
- **Response**: 429 Too Many Requests when limit exceeded

### 3. HTTP Method Validation
- **Purpose**: Only allows POST requests
- **Implementation**: Validates request method
- **Response**: 405 Method Not Allowed for non-POST requests

### 4. Input Validation
- **Purpose**: Prevents malicious input and ensures data integrity
- **Validations**:
  - Prompt must be a non-empty string
  - Prompt length limited to 500 characters
  - Tone must be one of: `professional`, `casual`, `witty`, `inspirational`
- **Response**: 400 Bad Request for invalid input

### 5. User-Agent Filtering
- **Purpose**: Blocks automated tools and scripts
- **Implementation**: Rejects requests from curl and similar tools
- **Note**: Only applies to same-host requests in development

## 🚫 What Gets Blocked

1. **Malicious Origins**: Requests from unauthorized domains
2. **Rate Limit Violations**: Too many requests from same IP
3. **Invalid Methods**: GET, PUT, DELETE, etc. (only POST allowed)
4. **Empty/Invalid Prompts**: Missing or overly long prompts
5. **Invalid Tones**: Tones not in the allowed list
6. **Automated Tools**: curl, wget, and similar tools (in development)

## ✅ What Gets Allowed

1. **Frontend Requests**: Requests from the Nuxt.js frontend
2. **Valid Input**: Properly formatted requests with valid data
3. **Within Rate Limits**: Requests that don't exceed rate limits
4. **Browser Requests**: Legitimate browser requests with proper headers

## 🔧 Configuration

### Development
- Allows localhost origins
- Includes debugging (commented out)
- More permissive for testing

### Production Setup
To configure for production:

1. **Update Allowed Origins**:
   ```typescript
   if (process.env.NODE_ENV === 'production') {
     allowedOrigins.push('https://yourdomain.com')
   }
   ```

2. **Use Redis for Rate Limiting**:
   Replace in-memory rate limiting with Redis for scalability

3. **Add HTTPS Enforcement**:
   Ensure all requests use HTTPS in production

4. **Monitor and Log**:
   Add proper logging for security events

## 🧪 Testing Security

The security measures can be tested using curl:

```bash
# Test malicious origin (should be blocked)
curl -X POST http://localhost:3000/api/generate \
  -H "Content-Type: application/json" \
  -H "Origin: https://malicious-site.com" \
  -d '{"prompt": "test", "tone": "professional"}'

# Test valid origin (should work)
curl -X POST http://localhost:3000/api/generate \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -H "User-Agent: Mozilla/5.0..." \
  -d '{"prompt": "test", "tone": "professional"}'

# Test invalid method (should be blocked)
curl -X GET http://localhost:3000/api/generate

# Test invalid input (should be blocked)
curl -X POST http://localhost:3000/api/generate \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -d '{"prompt": "", "tone": "invalid"}'
```

## 📝 Security Headers

Consider adding these security headers in production:

- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`

## 🔍 Monitoring

Monitor these metrics in production:
- Rate limit violations
- Origin validation failures
- Invalid input attempts
- API response times
- Error rates

## ⚠️ Important Notes

1. **API Key Security**: Ensure your Gemini API key is properly secured
2. **HTTPS Only**: Use HTTPS in production
3. **Regular Updates**: Keep dependencies updated
4. **Log Analysis**: Regularly review security logs
5. **Backup Plans**: Have rate limiting and DDoS protection at infrastructure level
