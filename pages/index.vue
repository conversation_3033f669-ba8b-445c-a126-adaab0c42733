<template>
    <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
      <!-- Form Header -->
      <div class="bg-gradient-to-r from-primary-500 to-secondary-500 p-6 text-white">
        <h2 class="text-2xl font-bold mb-2">Create Your Post</h2>
        <p class="text-primary-100">Tell us what you want to share with the world</p>
      </div>

      <!-- Form Content -->
      <form @submit.prevent="submitPrompt" class="p-8 space-y-8">
        <!-- Topic Input -->
        <div class="space-y-3">
          <label class="block text-lg font-semibold text-secondary-800 flex items-center gap-2">
            💡 What's your post about?
          </label>
          <textarea
            v-model="prompt"
            class="w-full p-4 border-2 border-secondary-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 resize-none bg-white/50 backdrop-blur-sm placeholder-secondary-400"
            rows="4"
            placeholder="Share your thoughts, ideas, or experiences... (e.g., 'Tips for remote work productivity' or 'My morning coffee routine')"
          ></textarea>
        </div>

        <!-- Tone Selection -->
        <div class="space-y-3">
          <label class="block text-lg font-semibold text-secondary-800 flex items-center gap-2">
            🎭 Choose your tone
          </label>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <label v-for="toneOption in toneOptions" :key="toneOption.value"
                   class="relative cursor-pointer">
              <input
                type="radio"
                :value="toneOption.value"
                v-model="tone"
                class="sr-only peer"
              >
              <div class="p-4 rounded-xl border-2 border-secondary-200 bg-white/50 backdrop-blur-sm text-center transition-all duration-200 peer-checked:border-primary-500 peer-checked:bg-primary-50 peer-checked:shadow-lg hover:border-primary-300 hover:shadow-md">
                <div class="text-2xl mb-2">{{ toneOption.emoji }}</div>
                <div class="font-medium text-secondary-700 peer-checked:text-primary-700">{{ toneOption.label }}</div>
                <div class="text-xs text-secondary-500 mt-1">{{ toneOption.description }}</div>
              </div>
            </label>
          </div>
        </div>

        <!-- Generate Button - Hidden when result is shown -->
        <button
          v-if="!result"
          type="submit"
          :disabled="loading || !prompt.trim()"
          class="w-full bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-bold py-4 px-6 rounded-xl hover:from-primary-600 hover:to-secondary-600 transition-all duration-200 transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl flex items-center justify-center gap-3"
        >
          <span v-if="!loading" class="text-xl">🚀</span>
          <span v-else class="animate-spin">⚡</span>
          {{ loading ? 'Generating your content...' : 'Generate Post' }}
        </button>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-8">
          <div class="inline-flex items-center gap-3 text-primary-600">
            <div class="animate-spin text-2xl">⚡</div>
            <span class="text-lg font-medium">Creating your amazing content...</span>
          </div>
        </div>

        <!-- Result Display -->
        <div v-if="result && !loading" class="mt-8 bg-gradient-to-br from-primary-50 to-secondary-50 p-6 rounded-2xl border-2 border-primary-200 shadow-inner">
          <div class="flex items-center gap-2 mb-4">
            <span class="text-2xl">✨</span>
            <h3 class="text-xl font-bold text-secondary-800">Your Generated Post</h3>
          </div>

          <div class="bg-white/80 backdrop-blur-sm p-5 rounded-xl border border-white/50 shadow-sm">
            <p class="text-secondary-700 whitespace-pre-line leading-relaxed text-lg">{{ result.text }}</p>
            <div v-if="result.hashtags" class="mt-4 pt-4 border-t border-secondary-200">
              <p class="text-primary-600 font-medium">{{ result.hashtags }}</p>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3 mt-4">
            <button
              type="button"
              @click="copyToClipboard"
              :class="[
                'flex-1 font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2',
                copied ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-white/80 backdrop-blur-sm text-secondary-700 border border-secondary-200 hover:bg-white hover:shadow-md'
              ]"
            >
              {{ copied ? '✅ Copied!' : '📋 Copy' }}
            </button>
            <button
              type="button"
              @click="generateAnother"
              :disabled="loading"
              class="flex-1 bg-primary-500 text-white font-medium py-2 px-4 rounded-lg hover:bg-primary-600 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="!loading">🔄</span>
              <span v-else class="animate-spin">⚡</span>
              {{ loading ? 'Generating...' : 'Generate Another' }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </template>
  
  <script setup lang="ts">
  const prompt = ref('')
  const tone = ref('professional')
  const result = ref<{ text: string; hashtags?: string } | null>(null)
  const loading = ref(false)
  const copied = ref(false)

  // Tone options with descriptions and emojis
  const toneOptions = [
    {
      value: 'professional',
      label: 'Professional',
      emoji: '💼',
      description: 'Formal & polished'
    },
    {
      value: 'casual',
      label: 'Casual',
      emoji: '😊',
      description: 'Friendly & relaxed'
    },
    {
      value: 'witty',
      label: 'Witty',
      emoji: '😄',
      description: 'Clever & humorous'
    },
    {
      value: 'inspirational',
      label: 'Inspirational',
      emoji: '🌟',
      description: 'Motivating & uplifting'
    }
  ]

  const submitPrompt = async () => {
    if (!prompt.value.trim()) return

    loading.value = true
    result.value = null

    try {
      const { data } = await useFetch('/api/generate', {
        method: 'POST',
        body: { prompt: prompt.value, tone: tone.value }
      })
      result.value = data.value || { text: 'No result generated. Please try again.' }
    } catch (error) {
      result.value = { text: 'Sorry, there was an error generating your post. Please try again.' }
    } finally {
      loading.value = false
    }
  }

  const copyToClipboard = async () => {
    if (!result.value) return

    const textToCopy = result.value.hashtags
      ? `${result.value.text}\n\n${result.value.hashtags}`
      : result.value.text

    try {
      await navigator.clipboard.writeText(textToCopy)
      copied.value = true
      // Reset the copied state after 2 seconds
      setTimeout(() => {
        copied.value = false
      }, 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const generateAnother = async () => {
    if (!prompt.value.trim() || loading.value) return

    // Reset copied state
    copied.value = false

    // Generate a new post with the same prompt and tone
    await submitPrompt()
  }
</script>